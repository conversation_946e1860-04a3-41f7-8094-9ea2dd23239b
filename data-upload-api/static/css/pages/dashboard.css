/* Dashboard Layout */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem 1rem 2.5rem;
    font-family: var(--font-family);
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.dashboard-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    position: relative;
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary);
    border-radius: 2px;
}

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--gray-100);
    color: var(--primary);
}

.date-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--gray-100);
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--gray-700);
}

/* Summary Cards */
.dashboard-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    border-radius: 12px;
    padding: 1.2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    color: white;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
    z-index: 1;
}

.summary-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    z-index: 1;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.summary-icon {
    font-size: 1.8rem;
    color: #fff;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 2;
}

.summary-content {
    flex: 1;
    z-index: 2;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.3rem;
}

.summary-card h2 {
    margin: 0;
    font-size: 1.8rem;
    color: #fff;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-card p {
    margin: 0;
    color: rgba(255,255,255,0.9);
    font-weight: 500;
    font-size: 0.9rem;
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.3rem 0.5rem;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
}

.trend-indicator.up {
    color: #e6ffea;
    background: rgba(76, 175, 80, 0.3);
}

.trend-indicator.down {
    color: #ffe6e6;
    background: rgba(244, 67, 54, 0.3);
}

.trend-indicator.warning {
    color: #fff8e1;
    background: rgba(255, 152, 0, 0.3);
}

.trend-indicator.neutral {
    color: rgba(255,255,255,0.9);
}

/* Gradient Backgrounds */
.gradient-blue {
    background: linear-gradient(135deg, #4361ee 0%, #4895ef 100%);
    box-shadow: 0 4px 20px rgba(67, 97, 238, 0.3);
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.gradient-orange {
    background: linear-gradient(135deg, #f9a826 0%, #ffbb4c 100%);
    box-shadow: 0 4px 20px rgba(249, 168, 38, 0.3);
    border: 1px solid rgba(249, 168, 38, 0.1);
}

.gradient-green {
    background: linear-gradient(135deg, #2ecc71 0%, #4cd97b 100%);
    box-shadow: 0 4px 20px rgba(46, 204, 113, 0.3);
    border: 1px solid rgba(46, 204, 113, 0.1);
}

.gradient-purple {
    background: linear-gradient(135deg, #8e44ad 0%, #a55eea 100%);
    box-shadow: 0 4px 20px rgba(142, 68, 173, 0.3);
    border: 1px solid rgba(142, 68, 173, 0.1);
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.dashboard-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
}

.dashboard-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    overflow: hidden;
    transition: all 0.3s;
    border: 1px solid rgba(0,0,0,0.05);
}

.dashboard-card:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    border-color: rgba(67, 97, 238, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.2rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(to right, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.5));
}

.card-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header h3 i {
    color: var(--primary);
    font-size: 1.2rem;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.4rem;
    border-radius: 6px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.card-body {
    padding: 1.2rem;
    background: white;
}

/* Activity Chart Card */
.activity-chart-card {
    grid-column: span 2;
}

.chart-period-selector {
    display: flex;
    align-items: center;
    background: var(--gray-100);
    border-radius: 6px;
    padding: 0.2rem;
}

.period-btn {
    background: none;
    border: none;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.period-btn.active {
    background: #fff;
    color: var(--primary);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Status Chart */
.status-chart-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.status-legend {
    display: flex;
    justify-content: center;
    gap: 1.2rem;
    flex-wrap: wrap;
}

.status-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-dot.completed {
    background: #2ecc71;
}

.status-dot.pending {
    background: #f9a826;
}

.status-dot.other {
    background: #95a5a6;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1.2rem 0.8rem;
    background: var(--gray-100);
    border-radius: 10px;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.3s;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(255,255,255,0) 60%);
    z-index: 1;
}

.quick-action-btn i {
    font-size: 1.8rem;
    color: var(--primary);
    transition: all 0.3s;
    position: relative;
    z-index: 2;
}

.quick-action-btn span {
    font-size: 0.9rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.quick-action-btn:hover {
    background: var(--primary-light);
    color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(67, 97, 238, 0.15);
    border-color: rgba(67, 97, 238, 0.1);
}

.quick-action-btn:hover i {
    transform: scale(1.1);
    color: var(--primary-dark);
}

/* Recent Activity */
.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.3rem;
    font-size: 0.9rem;
    color: var(--gray-800);
    line-height: 1.4;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* Insights Section */
.dashboard-insights {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

.insights-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(to right, rgba(249, 250, 251, 0.8), rgba(255, 255, 255, 0.8));
}

.insights-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.7rem;
}

.insights-header h3 i {
    color: #f9a826;
    font-size: 1.3rem;
}

.insights-body {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
}

.insight-card {
    display: flex;
    gap: 1rem;
    padding: 1.2rem;
    background: var(--gray-50);
    border-radius: 10px;
    transition: all 0.3s;
    border: 1px solid rgba(0,0,0,0.03);
    box-shadow: 0 2px 8px rgba(0,0,0,0.02);
    position: relative;
    overflow: hidden;
}

.insight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    z-index: 1;
}

.insight-card.warning::before {
    background: #f9a826;
}

.insight-card.info::before {
    background: var(--primary);
}

.insight-card.success::before {
    background: #2ecc71;
}

.insight-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.06);
}

.insight-icon {
    width: 46px;
    height: 46px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.insight-icon.warning {
    background: rgba(249, 168, 38, 0.15);
    color: #f9a826;
}

.insight-icon.info {
    background: rgba(67, 97, 238, 0.15);
    color: var(--primary);
}

.insight-icon.success {
    background: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
}

.insight-content {
    flex: 1;
}

.insight-content h4 {
    margin: 0 0 0.6rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
}

.insight-content p {
    margin: 0 0 0.8rem;
    font-size: 0.9rem;
    color: var(--gray-600);
    line-height: 1.5;
}

.insight-action {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.9rem;
    background: var(--primary-light);
    color: var(--primary);
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(67, 97, 238, 0.1);
}

.insight-action:hover {
    background: var(--primary);
    color: white;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

.insight-action::after {
    content: '→';
    font-size: 0.9rem;
    transition: transform 0.3s;
    display: inline-block;
}

.insight-action:hover::after {
    transform: translateX(3px);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #1a1a2e;
    color: #f1f1f1;
}

/* Dark Mode - Header */
body.dark-mode .dashboard-title {
    color: #f1f1f1;
}

body.dark-mode .theme-toggle {
    color: #f1f1f1;
}

body.dark-mode .theme-toggle:hover {
    background: #2a2a42;
}

body.dark-mode .date-display {
    background: #2a2a42;
    color: #f1f1f1;
}

/* Dark Mode - Cards and Containers */
body.dark-mode .dashboard-card,
body.dark-mode .dashboard-insights {
    background: #16213e;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    border-color: #2a2a42;
}

body.dark-mode .card-header,
body.dark-mode .insights-header {
    border-bottom: 1px solid #2a2a42;
    background: linear-gradient(to right, rgba(26, 26, 46, 0.8), rgba(22, 33, 62, 0.8));
}

body.dark-mode .card-body {
    background: #16213e;
}

body.dark-mode .card-header h3,
body.dark-mode .insights-header h3 {
    color: #f1f1f1;
}

body.dark-mode .card-header h3 i,
body.dark-mode .insights-header h3 i {
    color: #4cc9f0;
}

/* Dark Mode - Summary Cards */
body.dark-mode .summary-card {
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    border-color: rgba(255,255,255,0.05);
}

body.dark-mode .summary-card.gradient-blue {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    box-shadow: 0 4px 20px rgba(26, 35, 126, 0.4);
    border: 1px solid rgba(26, 35, 126, 0.2);
}

body.dark-mode .summary-card.gradient-orange {
    background: linear-gradient(135deg, #e65100 0%, #ef6c00 100%);
    box-shadow: 0 4px 20px rgba(230, 81, 0, 0.4);
    border: 1px solid rgba(230, 81, 0, 0.2);
}

body.dark-mode .summary-card.gradient-green {
    background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%);
    box-shadow: 0 4px 20px rgba(27, 94, 32, 0.4);
    border: 1px solid rgba(27, 94, 32, 0.2);
}

body.dark-mode .summary-card.gradient-purple {
    background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%);
    box-shadow: 0 4px 20px rgba(74, 20, 140, 0.4);
    border: 1px solid rgba(74, 20, 140, 0.2);
}

body.dark-mode .summary-card::before {
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
}

body.dark-mode .summary-icon {
    background: rgba(255,255,255,0.2);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

body.dark-mode .trend-indicator {
    background: rgba(255,255,255,0.15);
}

/* Dark Mode - Buttons and Controls */
body.dark-mode .btn-icon {
    color: #f1f1f1;
}

body.dark-mode .btn-icon:hover {
    background: #2a2a42;
    color: #4cc9f0;
}

body.dark-mode .chart-period-selector {
    background: #2a2a42;
}

body.dark-mode .period-btn {
    color: #f1f1f1;
}

body.dark-mode .period-btn.active {
    background: #16213e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    color: #4cc9f0;
}

/* Dark Mode - Charts */
body.dark-mode .status-legend-item {
    color: #f1f1f1;
}

body.dark-mode .status-dot {
    box-shadow: 0 0 5px rgba(0,0,0,0.3);
}

/* Dark Mode - Quick Actions */
body.dark-mode .quick-action-btn {
    background: #2a2a42;
    color: #f1f1f1;
    border-color: #3a3a62;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .quick-action-btn i {
    color: #4cc9f0;
}

body.dark-mode .quick-action-btn:hover {
    background: #3a3a72;
    border-color: #4cc9f0;
    box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

body.dark-mode .quick-action-btn:hover i {
    color: #4cc9f0;
}

/* Dark Mode - Activity List */
body.dark-mode .activity-item {
    border-bottom: 1px solid #2a2a42;
}

body.dark-mode .activity-icon {
    background: #2a2a42;
    color: #4cc9f0;
}

body.dark-mode .activity-content p {
    color: #f1f1f1;
}

body.dark-mode .activity-time {
    color: #a0a0a0;
}

body.dark-mode .empty-state {
    color: #a0a0a0;
}

/* Dark Mode - Insights */
body.dark-mode .insight-card {
    background: #2a2a42;
    border-color: #3a3a62;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .insight-card:hover {
    background: #3a3a72;
    box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

body.dark-mode .insight-icon {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .insight-icon.warning {
    background: rgba(249, 168, 38, 0.2);
}

body.dark-mode .insight-icon.info {
    background: rgba(76, 201, 240, 0.2);
    color: #4cc9f0;
}

body.dark-mode .insight-icon.success {
    background: rgba(46, 204, 113, 0.2);
}

body.dark-mode .insight-content h4 {
    color: #f1f1f1;
}

body.dark-mode .insight-content p {
    color: #d0d0d0;
}

body.dark-mode .insight-action {
    background: rgba(76, 201, 240, 0.2);
    color: #4cc9f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

body.dark-mode .insight-action:hover {
    background: #4cc9f0;
    color: #16213e;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-row {
        grid-template-columns: 1fr;
    }

    .activity-chart-card {
        grid-column: span 1;
    }

    .insights-body {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-controls {
        width: 100%;
        justify-content: space-between;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-legend {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .card-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 576px) {
    .dashboard-summary {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .activity-icon {
        margin-bottom: 0.5rem;
    }

    .insight-card {
        flex-direction: column;
    }

    .insight-icon {
        margin-bottom: 0.8rem;
    }
}
