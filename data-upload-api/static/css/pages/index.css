/* Modern Data Import Page Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.page-header h2 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1.75rem;
    display: flex;
    align-items: center;
}

.page-header h2 i {
    margin-right: var(--spacing-sm);
    color: var(--primary);
}

.page-subtitle {
    color: var(--gray-600);
    font-size: 1rem;
    margin-top: var(--spacing-xs);
}

/* Center the main container */
.main-index {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
}

/* Enhanced Card for File Upload and Request Type */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    transition: all var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary), var(--secondary));
    opacity: 0.8;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Form Section Styles */
.form-section {
    transition: all var(--transition);
    position: relative;
}

.form-section.hidden {
    opacity: 0;
    height: 0;
    margin: 0;
    overflow: hidden;
    transform: translateY(10px);
}

.form-section.visible {
    opacity: 1;
    animation: fade-in 0.5s ease-out;
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Labels */
.card label {
    font-weight: var(--font-weight-bold);
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--gray-700);
    font-size: 1.05rem;
    transition: all var(--transition-fast);
}

.required::after {
    content: " *";
    color: var(--danger);
    font-size: 1.2rem;
}

/* Form Controls */
.card select,
.card input[type="date"] {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
    background-color: white;
    box-shadow: var(--shadow-sm);
    color: var(--gray-800);
    height: 100%;
}

.card select:hover,
.card input[type="date"]:hover {
    border-color: var(--primary-light);
}

.card select:focus,
.card input[type="date"]:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    outline: none;
}

.card select:disabled {
    background-color: var(--gray-100);
    cursor: not-allowed;
    opacity: 0.7;
}

/* Enhanced Date Input */
.date-input-container {
    position: relative;
    width: 93%;
}

.date-input-container input[type="date"] {
    width: 100%;
    padding-right: 40px;
}

.date-input-container::after {
    content: '\f073';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    pointer-events: none;
}

/* Form Header */
.form-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.form-header h3 {
    margin: 0;
    color: var(--gray-800);
    font-size: 1.4rem;
    display: flex;
    align-items: center;
}

.form-header h3 i {
    margin-right: var(--spacing-sm);
    color: var(--secondary);
    font-size: 1.5rem;
}

/* Form Row */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.02), rgba(76, 201, 240, 0.02));
    transition: all var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(67, 97, 238, 0.1), transparent);
    transition: left 0.5s;
}

.file-upload-area:hover::before {
    left: 100%;
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(76, 201, 240, 0.05));
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.1), rgba(76, 201, 240, 0.1));
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    display: block;
    transition: all var(--transition);
}

.file-upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: var(--primary-dark);
}

.upload-text {
    font-size: 1.1rem;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-bold);
}

.upload-hint {
    font-size: 0.9rem;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.file-input {
    display: none;
}

.browse-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-bold);
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.browse-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.browse-button i {
    font-size: 1rem;
}

/* File Info Display */
.file-info {
    display: none;
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--gray-100);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--success);
}

.file-info.show {
    display: block;
    animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.file-details {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.file-icon {
    font-size: 2rem;
    color: var(--success);
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.file-size {
    font-size: 0.9rem;
    color: var(--gray-600);
}

.remove-file {
    background: var(--danger);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.85rem;
    transition: all var(--transition-fast);
}

.remove-file:hover {
    background: #d32f2f;
    transform: translateY(-1px);
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: all var(--transition);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    margin-top: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.submit-btn i {
    font-size: 1.2rem;
}

/* Ultra Modern Success Popup with Neumorphic Design */
.popup {
    position: fixed;
    top: 20px;
    right: 20px;
    color: var(--gray-800);
    padding: 24px;
    border-radius: 20px;
    z-index: 9999;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.08),
        0 5px 15px rgba(0, 0, 0, 0.03),
        inset 0 -2px 0 rgba(0, 0, 0, 0.05);
    width: 460px;
    font-family: var(--font-family);
    transform: translateX(400px) translateY(-10px);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    background: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.popup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 100%;
    background: linear-gradient(to bottom, #4CAF50, #2E7D32);
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}

.popup::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
    pointer-events: none;
    z-index: -1;
}

.popup.show {
    transform: translateX(0) translateY(0);
    animation: popup-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes popup-appear {
    0% { transform: translateX(400px) translateY(-10px); opacity: 0; }
    60% { transform: translateX(-15px) translateY(5px); opacity: 1; }
    80% { transform: translateX(5px) translateY(-2px); }
    100% { transform: translateX(0) translateY(0); }
}

.popup.hidden {
    display: none;
}

.popup .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
    color: #2E7D32;
    font-weight: 600;
    font-size: 1.25rem;
}

.popup .popup-header i {
    font-size: 1.5rem;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow:
        0 6px 12px rgba(76, 175, 80, 0.2),
        0 2px 4px rgba(76, 175, 80, 0.1);
}

.popup .popup-message {
    display: block;
    margin-bottom: 16px;
    line-height: 1.6;
    color: #424242;
    font-size: 1.05rem;
    padding: 4px 0;
    border-radius: 4px;
}

.popup .popup-close {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    font-size: 1rem;
    color: #757575;
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow:
        0 2px 5px rgba(0, 0, 0, 0.05),
        0 1px 2px rgba(0, 0, 0, 0.1);
}

.popup .popup-close:hover {
    color: #E53935;
    background-color: rgba(229, 57, 53, 0.1);
    transform: scale(1.1) rotate(90deg);
}

/* Progress bar inside popup */
.popup-progress {
    width: 100%;
    height: 6px;
    background-color: #f5f5f5;
    border-radius: 100px;
    overflow: hidden;
    margin-top: 16px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
}

.popup-progress-bar {
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #4CAF50, #2E7D32);
    animation: progressBar 3s cubic-bezier(0.1, 0.9, 0.2, 1) forwards;
    border-radius: 100px;
    position: relative;
    overflow: hidden;
}

.popup-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes progressBar {
    from { width: 100%; }
    to { width: 0%; }
}

/* Confirmation Modal Styles - Following Approval Page Pattern */
.confirmation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease;
    backdrop-filter: blur(5px);
}

.confirmation-modal-overlay.hidden {
    display: none;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.confirmation-modal-content {
    background-color: white;
    border-radius: 12px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
    from { transform: translateY(-70px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

@keyframes scaleDown {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0.95);
        opacity: 0;
    }
}

.confirmation-modal-header {
    padding: 25px 30px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.confirmation-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
}

.confirmation-modal-header i {
    font-size: 32px;
    z-index: 1;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.confirmation-modal-header h3 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    z-index: 1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    color: white;
}

.confirmation-modal-body {
    padding: 30px;
    font-size: 16px;
    color: #495057;
    line-height: 1.6;
    background-color: #f9fafc;
    border-bottom: 1px solid #eef1f6;
    max-height: 400px;
    overflow-y: auto;
}

.confirmation-modal-body p {
    margin: 0 0 10px 0;
}

.upload-details-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.confirmation-modal-actions {
    padding: 20px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background-color: white;
}

.confirmation-modal-cancel {
    padding: 12px 20px;
    background-color: #f1f5f9;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 5px;
}

.confirmation-modal-cancel:hover {
    background-color: #e2e8f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.confirmation-modal-proceed {
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
}

.confirmation-modal-proceed::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%);
    transition: all 0.8s ease;
}

.confirmation-modal-proceed:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
}

.confirmation-modal-proceed:hover::after {
    left: 100%;
}

/* Prevent body scroll when modal is active */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* Enhanced Form Steps Indicator */
.form-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: var(--spacing-md) 0 var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.form-steps::before {
    content: '';
    position: absolute;
    top: 36px;
    left: 12%;
    right: 12%;
    height: 2px;
    background-color: var(--gray-200);
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    width: 25%;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--gray-200);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
    border: 2px solid white;
    font-size: 0.9rem;
}

.step-label {
    font-size: 0.85rem;
    color: var(--gray-600);
    transition: all var(--transition);
    text-align: center;
    font-weight: var(--font-weight-normal);
}

.step.active .step-number {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.step.active .step-label {
    color: var(--primary);
    font-weight: var(--font-weight-bold);
}

.step.completed .step-number {
    background-color: var(--success);
    color: white;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.step.completed .step-label {
    color: var(--success);
    font-weight: var(--font-weight-bold);
}

.step.completed .step-number::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.request-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    transition: box-shadow var(--transition);
}

.request-container:hover {
    box-shadow: var(--shadow-md);
}
