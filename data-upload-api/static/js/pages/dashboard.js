// Dashboard JavaScript Module
document.addEventListener('DOMContentLoaded', function() {
    // Display current date
    const currentDateElement = document.getElementById('current-date');
    const now = new Date();
    const options = { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' };
    currentDateElement.textContent = now.toLocaleDateString('en-US', options);

    // Initialize charts
    initActivityChart();
    initStatusChart();

    // Set up event listeners
    setupEventListeners();

    // Check for saved theme preference
    checkThemePreference();
});

// Initialize the activity chart
function initActivityChart() {
    try {
        // Weekly data (default view)
        const weeklyLabels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
        const weeklyData = [12, 19, 7, 15, 10, 5, 8];

        // Monthly data
        const monthlyLabels = ["Week 1", "Week 2", "Week 3", "Week 4"];
        const monthlyData = [42, 38, 55, 29];

        const ctx = document.getElementById('activityChart');
        if (!ctx) {
            console.error('Activity chart canvas element not found');
            return;
        }

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return;
        }

        // Create gradient background
        const ctxObj = ctx.getContext('2d');
        const gradient = ctxObj.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(67, 97, 238, 0.3)');
        gradient.addColorStop(1, 'rgba(67, 97, 238, 0.02)');

        window.activityChart = new Chart(ctxObj, {
            type: 'line',
            data: {
                labels: weeklyLabels,
                datasets: [{
                    label: 'Files Uploaded',
                    data: weeklyData,
                    backgroundColor: gradient,
                    borderColor: '#4361ee',
                    borderWidth: 3,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#4361ee',
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        titleFont: {
                            weight: 'bold'
                        },
                        callbacks: {
                            label: function(context) {
                                return `Files: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: { display: false },
                        ticks: {
                            color: '#888',
                            font: { weight: 600 }
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        },
                        beginAtZero: true,
                        ticks: {
                            color: '#888',
                            font: { weight: 600 },
                            precision: 0
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    line: {
                        borderJoinStyle: 'round'
                    }
                }
            }
        });

        // Store data for period switching
        window.chartData = {
            weekly: {
                labels: weeklyLabels,
                data: weeklyData
            },
            monthly: {
                labels: monthlyLabels,
                data: monthlyData
            }
        };
    } catch (error) {
        console.error('Error initializing activity chart:', error);
    }
}

// Initialize the status distribution chart
function initStatusChart() {
    try {
        const statusCanvas = document.getElementById('statusChart');
        if (!statusCanvas) {
            console.error('Status chart canvas element not found');
            return;
        }

        const statusCtx = statusCanvas.getContext('2d');

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return;
        }

        // Get values from the summary data (these will be replaced by template variables)
        const completedCount = parseInt(window.dashboardData?.completed || 0) || 0;
        const pendingCount = parseInt(window.dashboardData?.pending || 0) || 0;
        const totalCount = parseInt(window.dashboardData?.requests || 0) || 0;
        const otherCount = Math.max(0, totalCount - completedCount - pendingCount);

        // If all values are 0, show a placeholder chart
        const hasData = completedCount > 0 || pendingCount > 0 || otherCount > 0;

        const chartData = hasData ?
            [completedCount, pendingCount, otherCount] :
            [1, 1, 1]; // Placeholder data if no real data

        const chartColors = [
            '#2ecc71',  // Green for completed
            '#f9a826',  // Orange for pending
            '#95a5a6'   // Gray for other
        ];

        // Add subtle patterns to the chart segments
        const patternColors = chartColors.map(color => {
            const colorWithOpacity = color.replace(')', ', 0.8)').replace('rgb', 'rgba');
            return colorWithOpacity;
        });

        window.statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'Pending', 'Other'],
                datasets: [{
                    data: chartData,
                    backgroundColor: patternColors,
                    borderColor: '#fff',
                    borderWidth: 2,
                    hoverOffset: 10,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: hasData, // Disable tooltips for placeholder data
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });

        // Add "No data" text if using placeholder data
        if (!hasData) {
            const width = statusCanvas.width;
            const height = statusCanvas.height;
            statusCtx.font = '14px Arial';
            statusCtx.fillStyle = '#888';
            statusCtx.textAlign = 'center';
            statusCtx.textBaseline = 'middle';
            statusCtx.fillText('No data available', width / 2, height / 2);
        }
    } catch (error) {
        console.error('Error initializing status chart:', error);
    }
}

// Set up event listeners for interactive elements
function setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Chart period selector
    const periodButtons = document.querySelectorAll('.period-btn');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            const period = this.getAttribute('data-period');
            updateChartPeriod(period);

            // Update active state
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Refresh buttons
    const refreshChartBtn = document.getElementById('refresh-chart');
    if (refreshChartBtn) {
        refreshChartBtn.addEventListener('click', function() {
            refreshChart();
            this.classList.add('rotating');
            setTimeout(() => {
                this.classList.remove('rotating');
            }, 1000);
        });
    }

    const refreshActivityBtn = document.getElementById('refresh-activity');
    if (refreshActivityBtn) {
        refreshActivityBtn.addEventListener('click', function() {
            // In a real app, this would fetch new activity data from the server
            this.classList.add('rotating');
            setTimeout(() => {
                this.classList.remove('rotating');
            }, 1000);
        });
    }
}

// Toggle between light and dark mode
function toggleTheme() {
    try {
        const body = document.body;
        const themeIcon = document.querySelector('.theme-toggle i');

        if (body.classList.contains('dark-mode')) {
            body.classList.remove('dark-mode');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-mode');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');
        }

        // Update charts for the new theme
        updateChartsForTheme();
    } catch (error) {
        console.error('Error toggling theme:', error);
    }
}

// Check for saved theme preference
function checkThemePreference() {
    try {
        const savedTheme = localStorage.getItem('theme');
        const themeIcon = document.querySelector('.theme-toggle i');

        if (savedTheme === 'dark') {
            document.body.classList.add('dark-mode');
            if (themeIcon) {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // We need to wait for charts to initialize before updating them
            setTimeout(() => {
                updateChartsForTheme();
            }, 500);
        }
    } catch (error) {
        console.error('Error checking theme preference:', error);
    }
}

// Update chart colors based on theme
function updateChartsForTheme() {
    try {
        const isDarkMode = document.body.classList.contains('dark-mode');

        // Update activity chart if it exists
        if (window.activityChart) {
            // Update grid lines
            const gridColor = isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
            const tickColor = isDarkMode ? '#aaa' : '#888';

            window.activityChart.options.scales.x.grid.color = gridColor;
            window.activityChart.options.scales.y.grid.color = gridColor;

            // Update tick colors
            window.activityChart.options.scales.x.ticks.color = tickColor;
            window.activityChart.options.scales.y.ticks.color = tickColor;

            // Update tooltip styles
            window.activityChart.options.plugins.tooltip.backgroundColor = isDarkMode ? 'rgba(40, 44, 52, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            window.activityChart.options.plugins.tooltip.titleColor = isDarkMode ? '#f1f1f1' : '#333';
            window.activityChart.options.plugins.tooltip.bodyColor = isDarkMode ? '#d0d0d0' : '#666';
            window.activityChart.options.plugins.tooltip.borderColor = isDarkMode ? '#3a3a62' : '#ddd';

            window.activityChart.update();
        }

        // Update status chart if it exists
        if (window.statusChart) {
            // Update tooltip styles
            window.statusChart.options.plugins.tooltip.backgroundColor = isDarkMode ? 'rgba(40, 44, 52, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            window.statusChart.options.plugins.tooltip.titleColor = isDarkMode ? '#f1f1f1' : '#333';
            window.statusChart.options.plugins.tooltip.bodyColor = isDarkMode ? '#d0d0d0' : '#666';
            window.statusChart.options.plugins.tooltip.borderColor = isDarkMode ? '#3a3a62' : '#ddd';

            // Update border color
            window.statusChart.data.datasets[0].borderColor = isDarkMode ? '#2a2a42' : '#fff';

            window.statusChart.update();
        }
    } catch (error) {
        console.error('Error updating charts for theme change:', error);
    }
}

// Update chart data based on selected period
function updateChartPeriod(period) {
    if (!window.activityChart || !window.chartData) return;

    const chartData = window.chartData[period];
    if (!chartData) return;

    window.activityChart.data.labels = chartData.labels;
    window.activityChart.data.datasets[0].data = chartData.data;
    window.activityChart.update();
}

// Simulate refreshing chart data
function refreshChart() {
    if (!window.activityChart) return;

    // Generate random data for demonstration
    const newData = Array.from({length: window.activityChart.data.labels.length}, () =>
        Math.floor(Math.random() * 20) + 5
    );

    // Animate the transition to new data
    window.activityChart.data.datasets[0].data = newData;
    window.activityChart.update();
}

// Add rotating animation for refresh buttons
document.head.insertAdjacentHTML('beforeend', `
<style>
@keyframes rotating {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
.rotating {
    animation: rotating 1s linear;
}
</style>
`);
