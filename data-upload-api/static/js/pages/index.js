// Index Page JavaScript Module
$(document).ready(function () {
    let supplierMap = {}; // Store Supplier ID -> Name mapping
    let currentStep = 1; // Track current form step

    // Make sure the popups are properly initialized
    $("#successPopup").removeClass("show").addClass("hidden");
    $("#confirmationModal").addClass("hidden");

    // Reset the form to its initial state
    function resetForm() {
        $("#supplierId").val("");
        $("#requestId").val("").prop("disabled", true);
        $("#fileInput").val("");
        // Reset the date picker
        $("#datePicker").val("");
        $("#uploadBtn").prop("disabled", true);

        // Reset drag and drop area
        const dragDropArea = $(".drag-drop-area");
        dragDropArea.removeClass("has-file");
        dragDropArea.find("i").removeClass("fa-file-check").addClass("fa-cloud-upload-alt");
        dragDropArea.find("p").first().text("Drag and drop your file here or click to browse");
        dragDropArea.find(".file-size").remove();

        // Reset form steps
        updateFormSteps(1);
        currentStep = 1;

        // Hide file upload section
        $("#fileUploadSection").removeClass("visible").addClass("hidden");

        // Clear any error messages
        $("#message").removeClass("show-error").hide();
    }

    // Update form steps indicator
    function updateFormSteps(step) {
        $(".step").removeClass("active completed");
        
        for (let i = 1; i <= 4; i++) {
            const stepElement = $(`.step:nth-child(${i})`);
            if (i < step) {
                stepElement.addClass("completed");
            } else if (i === step) {
                stepElement.addClass("active");
            }
        }
        currentStep = step;
    }

    // Load suppliers on page load
    $.get("/api/suppliers", function (data) {
        const supplierSelect = $("#supplierId");
        supplierSelect.empty().append('<option value="">Select a supplier</option>');
        
        data.forEach(function (supplier) {
            supplierMap[supplier.id] = supplier.name;
            supplierSelect.append(`<option value="${supplier.id}">${supplier.name}</option>`);
        });
    }).fail(function() {
        showMessage("Failed to load suppliers. Please refresh the page.", "error");
    });

    // Handle supplier selection
    $("#supplierId").change(function () {
        const supplierId = $(this).val();
        const requestSelect = $("#requestId");
        
        if (supplierId) {
            updateFormSteps(2);
            requestSelect.prop("disabled", false).empty().append('<option value="">Loading...</option>');
            
            $.get(`/api/request-types/${supplierId}`, function (data) {
                requestSelect.empty().append('<option value="">Select a request type</option>');
                data.forEach(function (requestType) {
                    requestSelect.append(`<option value="${requestType.id}">${requestType.name}</option>`);
                });
            }).fail(function() {
                requestSelect.empty().append('<option value="">Failed to load request types</option>');
                showMessage("Failed to load request types. Please try again.", "error");
            });
        } else {
            requestSelect.val("").prop("disabled", true).empty().append('<option value="">Select a request type</option>');
            $("#fileUploadSection").removeClass("visible").addClass("hidden");
            updateFormSteps(1);
        }
    });

    // Handle request type selection
    $("#requestId").change(function () {
        const requestId = $(this).val();
        if (requestId) {
            updateFormSteps(3);
            $("#fileUploadSection").removeClass("hidden").addClass("visible");
        } else {
            $("#fileUploadSection").removeClass("visible").addClass("hidden");
            updateFormSteps(2);
        }
    });

    // File input change handler
    $("#fileInput").change(function () {
        const file = this.files[0];
        if (file) {
            handleFileSelection(file);
        }
    });

    // Handle file selection (both drag-drop and file input)
    function handleFileSelection(file) {
        const dragDropArea = $(".drag-drop-area");
        const maxSize = 50 * 1024 * 1024; // 50MB
        
        // Validate file size
        if (file.size > maxSize) {
            showMessage("File size exceeds 50MB limit. Please select a smaller file.", "error");
            return;
        }

        // Validate file type
        const allowedTypes = ['.xlsx', '.xls', '.csv'];
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));
        
        if (!isValidType) {
            showMessage("Invalid file type. Please select an Excel (.xlsx, .xls) or CSV file.", "error");
            return;
        }

        // Update drag-drop area appearance
        dragDropArea.addClass("has-file");
        dragDropArea.find("i").removeClass("fa-cloud-upload-alt").addClass("fa-file-check");
        dragDropArea.find("p").first().text(file.name);
        
        // Add file size info
        const fileSize = formatFileSize(file.size);
        if (dragDropArea.find(".file-size").length === 0) {
            dragDropArea.append(`<div class="file-size">${fileSize}</div>`);
        } else {
            dragDropArea.find(".file-size").text(fileSize);
        }

        // Enable upload button
        $("#uploadBtn").prop("disabled", false);
        updateFormSteps(4);
        
        // Clear any previous error messages
        $("#message").removeClass("show-error").hide();
    }

    // Format file size for display
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Drag and drop functionality
    const dragDropArea = $(".drag-drop-area");
    
    dragDropArea.on("dragover", function (e) {
        e.preventDefault();
        $(this).addClass("dragover");
    });

    dragDropArea.on("dragleave", function (e) {
        e.preventDefault();
        $(this).removeClass("dragover");
    });

    dragDropArea.on("drop", function (e) {
        e.preventDefault();
        $(this).removeClass("dragover");
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            $("#fileInput")[0].files = files; // Update the file input
            handleFileSelection(file);
        }
    });

    // Click to browse functionality
    dragDropArea.on("click", function () {
        $("#fileInput").click();
    });

    // Show message function
    function showMessage(message, type = "error") {
        const messageElement = $("#message");
        messageElement.text(message);
        
        if (type === "error") {
            messageElement.addClass("show-error").show();
        }
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageElement.removeClass("show-error").hide();
        }, 5000);
    }

    // Upload button click handler
    $("#uploadBtn").click(function (e) {
        e.preventDefault();
        
        const supplierId = $("#supplierId").val();
        const requestId = $("#requestId").val();
        const file = $("#fileInput")[0].files[0];
        const datePicker = $("#datePicker").val();

        // Validation
        if (!supplierId || !requestId || !file) {
            showMessage("Please fill in all required fields and select a file.", "error");
            return;
        }

        // Show confirmation modal
        showConfirmationModal(supplierId, requestId, file, datePicker);
    });

    // Show confirmation modal
    function showConfirmationModal(supplierId, requestId, file, datePicker) {
        const supplierName = supplierMap[supplierId] || "Unknown Supplier";
        const requestTypeName = $("#requestId option:selected").text();
        
        // Update modal content
        $("#modalSupplierName").text(supplierName);
        $("#modalRequestType").text(requestTypeName);
        $("#modalFileName").text(file.name);
        $("#modalFileSize").text(formatFileSize(file.size));
        
        if (datePicker) {
            $("#modalDatePicker").text(new Date(datePicker).toLocaleDateString());
            $("#modalDateRow").show();
        } else {
            $("#modalDateRow").hide();
        }

        // Show modal
        $("#confirmationModal").removeClass("hidden");
        $("body").addClass("modal-open");
    }

    // Modal cancel button
    $(".confirmation-modal-cancel").click(function () {
        $("#confirmationModal").addClass("hidden");
        $("body").removeClass("modal-open");
    });

    // Modal proceed button
    $(".confirmation-modal-proceed").click(function () {
        $("#confirmationModal").addClass("hidden");
        $("body").removeClass("modal-open");
        performUpload();
    });

    // Perform the actual upload
    function performUpload() {
        const formData = new FormData();
        const supplierId = $("#supplierId").val();
        const requestId = $("#requestId").val();
        const file = $("#fileInput")[0].files[0];
        const datePicker = $("#datePicker").val();

        formData.append("supplier_id", supplierId);
        formData.append("request_id", requestId);
        formData.append("file", file);
        if (datePicker) {
            formData.append("date_picker", datePicker);
        }

        // Show loader
        $("#loader").show();
        $("#uploadBtn").prop("disabled", true);

        $.ajax({
            url: "/upload",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                $("#loader").hide();
                showSuccessPopup(response.message || "File uploaded successfully!");
                resetForm();
            },
            error: function (xhr) {
                $("#loader").hide();
                $("#uploadBtn").prop("disabled", false);
                
                let errorMessage = "Upload failed. Please try again.";
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }
                showMessage(errorMessage, "error");
            }
        });
    }

    // Show success popup
    function showSuccessPopup(message) {
        const popup = $("#successPopup");
        popup.find(".popup-message").text(message);
        popup.removeClass("hidden").addClass("show");
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            popup.removeClass("show").addClass("hidden");
        }, 3000);
    }

    // Success popup close button
    $(".popup-close").click(function () {
        $("#successPopup").removeClass("show").addClass("hidden");
    });

    // Close modal when clicking outside
    $(document).click(function (e) {
        if ($(e.target).hasClass("confirmation-modal-overlay")) {
            $("#confirmationModal").addClass("hidden");
            $("body").removeClass("modal-open");
        }
    });

    // Initialize form steps
    updateFormSteps(1);
});
