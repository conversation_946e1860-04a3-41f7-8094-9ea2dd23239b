{% extends "base.html" %}
{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="dashboard-title">Dashboard</h1>
        <div class="dashboard-controls">
            <button id="theme-toggle" class="theme-toggle" title="Toggle dark/light mode">
                <i class="fas fa-moon"></i>
            </button>
            <div class="date-display">
                <i class="far fa-calendar-alt"></i>
                <span id="current-date"></span>
            </div>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="dashboard-summary">
        <!-- Files Uploaded Card -->
        <div class="summary-card gradient-blue">
            <div class="summary-icon"><i class="fas fa-file-upload"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.files_uploaded }}</h2>
                    {% if summary.today is defined and summary.today > 0 %}
                    <div class="trend-indicator up">
                        <i class="fas fa-arrow-up"></i>
                        <span>{{ summary.today }}</span>
                    </div>
                    {% else %}
                    <div class="trend-indicator neutral">
                        <i class="fas fa-minus"></i>
                        <span>0</span>
                    </div>
                    {% endif %}
                </div>
                <p>Files Uploaded</p>
            </div>
        </div>

        <!-- Pending Approvals Card -->
        <div class="summary-card gradient-orange">
            <div class="summary-icon"><i class="fas fa-hourglass-half"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.pending_approvals }}</h2>
                    <div class="trend-indicator {% if summary.pending_approvals > 5 %}warning{% else %}neutral{% endif %}">
                        <i class="fas {% if summary.pending_approvals > 5 %}fa-exclamation-triangle{% else %}fa-check{% endif %}"></i>
                    </div>
                </div>
                <p>Pending Approvals</p>
            </div>
        </div>

        <!-- Total Requests Card -->
        <div class="summary-card gradient-green">
            <div class="summary-icon"><i class="fas fa-tasks"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.requests }}</h2>
                    <div class="trend-indicator neutral">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <p>Total Requests</p>
            </div>
        </div>

        <!-- Suppliers Card -->
        <div class="summary-card gradient-purple">
            <div class="summary-icon"><i class="fas fa-industry"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.suppliers }}</h2>
                    <div class="trend-indicator neutral">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <p>Suppliers</p>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="dashboard-grid">
        <!-- Activity Chart Card -->
        <div class="dashboard-card activity-chart-card">
            <div class="card-header">
                <h3><i class="fas fa-chart-line"></i> Activity Overview</h3>
                <div class="card-actions">
                    <button class="btn-icon" id="refresh-chart" title="Refresh data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <div class="chart-period-selector">
                        <button class="period-btn active" data-period="week">Week</button>
                        <button class="period-btn" data-period="month">Month</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="100"></canvas>
            </div>
        </div>

        <!-- Two Column Layout for Second Row -->
        <div class="dashboard-row">
            <!-- Status Chart Card -->
            <div class="dashboard-card status-chart-card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-pie"></i> Status Distribution</h3>
                </div>
                <div class="card-body">
                    <div class="status-chart-container">
                        <canvas id="statusChart" height="180"></canvas>
                    </div>
                    <div class="status-legend">
                        <div class="status-legend-item">
                            <span class="status-dot completed"></span>
                            <span>Completed ({{ summary.completed|default(0) }})</span>
                        </div>
                        <div class="status-legend-item">
                            <span class="status-dot pending"></span>
                            <span>Pending ({{ summary.pending|default(0) }})</span>
                        </div>
                        <div class="status-legend-item">
                            <span class="status-dot other"></span>
                            <span>Other ({{ summary.requests - (summary.completed|default(0)) - (summary.pending|default(0)) }})</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="dashboard-card quick-actions-card">
                <div class="card-header">
                    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="quick-actions-grid">
                        <a href="/" class="quick-action-btn">
                            <i class="fas fa-file-upload"></i>
                            <span>Upload Data</span>
                        </a>
                        <a href="/view_requests" class="quick-action-btn">
                            <i class="fas fa-list"></i>
                            <span>View Requests</span>
                        </a>
                        <a href="/approval" class="quick-action-btn">
                            <i class="fas fa-check-circle"></i>
                            <span>Approvals</span>
                        </a>
                        <a href="/api/files" class="quick-action-btn">
                            <i class="fas fa-folder-open"></i>
                            <span>Files</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Card -->
        <div class="dashboard-card recent-activity-card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> Recent Activity</h3>
                <div class="card-actions">
                    <button class="btn-icon" id="refresh-activity" title="Refresh activity">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if recent_activity %}
                    <ul class="activity-list">
                        {% for activity in recent_activity %}
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="activity-content">
                                    <p>{{ activity }}</p>
                                    <span class="activity-time">{{ loop.index0 * 2 + 1 }} hour{% if loop.index0 > 0 %}s{% endif %} ago</span>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="empty-state">
                        <i class="far fa-calendar-times"></i>
                        <p>No recent activity</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Insights Section -->
    <div class="dashboard-insights">
        <div class="insights-header">
            <h3><i class="fas fa-lightbulb"></i> Insights & Recommendations</h3>
        </div>
        <div class="insights-body">
            <div class="insight-card warning">
                <div class="insight-icon warning">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>Pending Approvals</h4>
                    <p>You have {{ summary.pending_approvals }} pending approval{% if summary.pending_approvals != 1 %}s{% endif %} that require attention.</p>
                    <a href="/approval" class="insight-action">Review Now</a>
                </div>
            </div>
            <div class="insight-card info">
                <div class="insight-icon info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>Upload Activity</h4>
                    <p>{{ summary.today|default(0) }} new file{% if summary.today|default(0) != 1 %}s{% endif %} uploaded today. {{ summary.completed|default(0) }} request{% if summary.completed|default(0) != 1 %}s{% endif %} completed successfully.</p>
                </div>
            </div>
            <div class="insight-card success">
                <div class="insight-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>System Status</h4>
                    <p>All systems operational. Data processing is running efficiently.</p>
                    <a href="/api" class="insight-action">View Details</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="/static/css/pages/dashboard.css">



{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Pass dashboard data to JavaScript
window.dashboardData = {
    completed: {{ summary.completed|default(0) }},
    pending: {{ summary.pending|default(0) }},
    requests: {{ summary.requests|default(0) }}
};
</script>
<script src="/static/js/pages/dashboard.js"></script>
{% endblock %}
