{% extends "base.html" %}

{% block title %}Data Import{% endblock %}
{% block extra_head %}
<link rel="stylesheet" href="/static/css/pages/index.css">





{% endblock %}

{% block content %}
<div class="request-container">
    <div class="main-index">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h2><i class="fas fa-file-upload"></i> Data Import</h2>
                <div class="page-subtitle">Upload your data files for processing</div>
            </div>
        </div>

        <!-- File Upload Form Header -->
        <div class="form-header">
            <h3><i class="fas fa-cloud-upload-alt"></i> File Upload Form</h3>
        </div>

        <div class="card">
            <!-- Form Steps Indicator -->
            <div class="form-steps">
                <div class="step active" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-label">Supplier</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-label">Request Type</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-label">File</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-label">Upload</div>
                </div>
            </div>

            <!-- Supplier Selection -->
            <div class="form-section supplier-box visible">
                <div class="form-section-header">
                    <i class="fas fa-building form-section-icon"></i>
                    <label for="supplierId" class="required">Select Supplier</label>
                </div>
                <p class="form-section-help">Choose the supplier for this data import</p>
                <select id="supplierId">
                    <option value="">-- Loading Suppliers... --</option>
                </select>
            </div>

            <!-- Request Type Section (Initially Hidden) -->
            <div class="form-section request-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-file-import form-section-icon"></i>
                    <label for="requestId" class="required">Select Request Type</label>
                </div>
                <p class="form-section-help">Choose the type of data you're importing</p>
                <select id="requestId" disabled>
                    <option value="">-- Select Request Type --</option>
                    <option value="1">All Importing</option>
                    <option value="2">Product Importing</option>
                    <option value="3">Price Importing</option>
                    <option value="4">Stock Importing</option>
                </select>
            </div>

            <!-- File Upload Section (Initially Hidden) -->
            <div class="form-section upload-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-file-excel form-section-icon"></i>
                    <label for="fileInput" class="required">Choose a File</label>
                </div>
                <p class="form-section-help">Select the data file you want to upload</p>
                <!-- Drag & Drop Area -->
                <div class="drag-drop-area" id="dragDropArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <div>
                        Drag & Drop a file here<br>
                        <strong>or click to browse</strong>
                    </div>
                    <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
                </div>
                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" style="display: none;">
            </div>

            <!-- Created Date Picker (Initially Hidden) -->
            <div class="form-section date-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-calendar-alt form-section-icon"></i>
                    <label for="datePicker">Select Created Date (Optional)</label>
                </div>
                <p class="form-section-help">Choose a specific date for this data import</p>
                <div class="date-input-container">
                    <input type="date" id="datePicker" class="date-input" placeholder="YYYY-MM-DD">
                </div>
            </div>

            <!-- Upload Button -->
            <button id="uploadBtn" disabled>
                <i class="fas fa-upload"></i> Upload File
            </button>

            <!-- Loader (Initially hidden) -->
            <div id="loader" style="display: none;">
                <div class="normal-spinner"></div>
                <span>Uploading file, please wait...</span>
            </div>

            <!-- Response Message (for errors) -->
            <p id="message"></p>
        </div>

        <!-- Upload Confirmation Modal -->
        <div id="confirmationModal" class="confirmation-modal-overlay hidden">
            <div class="confirmation-modal-content">
                <div class="confirmation-modal-header">
                    <i class="fas fa-exclamation-circle"></i>
                    <h3>Confirm Upload</h3>
                </div>
                <div class="confirmation-modal-body">
                    <p>Please review the upload details before proceeding:</p>
                    <div id="uploadDetails" class="upload-details-container">
                        <!-- Upload details will be populated here -->
                    </div>
                    <p style="margin-top: 16px;">
                        <i class="fas fa-info-circle" style="color: #4361ee; margin-right: 8px;"></i>
                        This action will upload and process your file. Make sure all details are correct.
                    </p>
                </div>
                <div class="confirmation-modal-actions">
                    <button id="confirmationCancel" class="confirmation-modal-cancel">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button id="confirmationProceed" class="confirmation-modal-proceed">
                        <i class="fas fa-check"></i> Proceed with Upload
                    </button>
                </div>
            </div>
        </div>

        <!-- Ultra Modern Success Popup with Neumorphic Design -->
        <div id="successPopup" class="popup hidden">
            <div class="popup-header">
                <i class="fas fa-check-circle"></i>
                <span>Upload Successful</span>
            </div>
            <button class="popup-close"><i class="fas fa-times"></i></button>
            <div class="popup-message"></div>
            <div class="popup-progress">
                <div class="popup-progress-bar"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    $(document).ready(function () {
        let supplierMap = {}; // Store Supplier ID -> Name mapping
        let currentStep = 1; // Track current form step

        // Make sure the popups are properly initialized
        $("#successPopup").removeClass("show").addClass("hidden");
        $("#confirmationModal").addClass("hidden");

        // Reset the form to its initial state
        function resetForm() {
            $("#supplierId").val("");
            $("#requestId").val("").prop("disabled", true);
            $("#fileInput").val("");
            // Reset the date picker
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset form sections
            $(".form-section").removeClass("visible").addClass("hidden");
            $(".supplier-box").removeClass("hidden").addClass("visible");

            // Reset steps
            updateFormSteps(1);

            // Clear message
            $("#message").text("").removeClass("show-error");
            $("#loader").hide();

            // Reset drag-drop area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);
        }

        // Update form steps indicator
        function updateFormSteps(step) {
            currentStep = step;

            // Reset all steps
            $(".step").removeClass("active completed");

            // Mark current step as active
            $(`#step${step}`).addClass("active");

            // Mark previous steps as completed
            for (let i = 1; i < step; i++) {
                $(`#step${i}`).removeClass("active").addClass("completed");
            }
        }

        // Immediately reset the form on page load (handles soft reloads)
        resetForm();

        // Validate file extensions
        function isValidFileType(fileName) {
            let allowedExtensions = ['xlsx', 'xls', 'csv'];
            let fileExtension = fileName.split('.').pop().toLowerCase();
            return allowedExtensions.includes(fileExtension);
        }

        // Enable Upload button if a request is selected and a file is chosen
        function checkEnableUploadButton() {
            let requestSelected = $("#requestId").val();
            let fileSelected = $("#fileInput")[0].files.length > 0;
            $("#uploadBtn").prop("disabled", !(requestSelected && fileSelected));

            // Update step 4 if all conditions are met
            if (requestSelected && fileSelected) {
                updateFormSteps(4);
            }
        }

        // Enhanced loader functions for the form loader
        function showLoader() {
            // Show only the form loader, not the main page loader
            $("#loader").fadeIn(300);
            $("#uploadBtn").prop("disabled", true);
        }

        function hideLoader() {
            // Hide the form loader
            $("#loader").fadeOut(300);

            // Re-enable the upload button if conditions are met
            checkEnableUploadButton();
        }

        // Fetch suppliers from the server
        function fetchSuppliers() {
            $.ajax({
                url: "/api/suppliers",
                type: "GET",
                success: function (response) {
                    let supplierDropdown = $("#supplierId");
                    supplierDropdown.empty();
                    supplierDropdown.append('<option value="">-- Select Supplier --</option>');

                    // Sort suppliers alphabetically by display name
                    response.sort((a, b) => a.display_name.localeCompare(b.display_name));

                    response.forEach(function (supplier) {
                        supplierMap[supplier.supplier_id] = supplier.display_name;
                        supplierDropdown.append(`<option value="${supplier.supplier_id}">${supplier.display_name}</option>`);
                    });
                },
                error: function () {
                    $("#supplierId").html('<option value="">-- Failed to Load Suppliers --</option>');
                    $("#message").text("Failed to load suppliers. Please refresh the page.").addClass("show-error");
                }
            });
        }

        // Supplier change event: reveal request type section and reset subsequent steps
        $("#supplierId").change(function () {
            let supplierSelected = $(this).val();

            // Reset subsequent form sections regardless of selection
            $("#requestId").val("").prop("disabled", true);
            $("#fileInput").val("");
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset file upload area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);

            // Hide subsequent sections
            $(".upload-box, .date-box").removeClass("visible").addClass("hidden");

            // Clear any error messages
            $("#message").text("").removeClass("show-error");

            if (supplierSelected) {
                // Show request type section with animation
                $(".request-box").removeClass("hidden").addClass("visible");
                $("#requestId").prop("disabled", false);

                // Update step indicator
                updateFormSteps(2);
            } else {
                resetForm();
            }
        });

        // Request type change event: reveal file upload section and reset subsequent steps
        $("#requestId").change(function () {
            let requestSelected = $(this).val();

            // Reset file upload related fields
            $("#fileInput").val("");
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset file upload area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);

            // Clear any error messages
            $("#message").text("").removeClass("show-error");

            if (requestSelected) {
                // Show file upload and date sections with animation
                $(".upload-box, .date-box").removeClass("hidden").addClass("visible");

                // Update step indicator
                updateFormSteps(3);
            } else {
                // Hide file upload and date sections
                $(".upload-box, .date-box").removeClass("visible").addClass("hidden");

                // Update step indicator back to supplier selection
                updateFormSteps(2);
            }

            checkEnableUploadButton();
        });

        // File input change event: validate file and update drag-drop area
        $("#fileInput").change(function () {
            let file = this.files[0];
            if (file) {
                if (!isValidFileType(file.name)) {
                    $("#message").text("Invalid file type! Please select an Excel or CSV file.").addClass("show-error");
                    $("#fileInput").val("");
                    $("#uploadBtn").prop("disabled", true);
                    $("#dragDropArea").removeClass("has-file");
                    return;
                }

                $("#message").text("").removeClass("show-error");

                // Update drag-drop area with file name
                $("#dragDropArea").addClass("has-file").html(`
                    <i class="fas fa-file-alt"></i>
                    <div>${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                `);
            }

            checkEnableUploadButton();
        });

        // Format file size for display
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Enhanced Drag & Drop Functionality
        let dropArea = $("#dragDropArea");

        dropArea.off("click").on("click", function () {
            $("#fileInput").click();
        });

        dropArea.on("dragover", function (e) {
            e.preventDefault();
            $(this).addClass("dragover");
        });

        dropArea.on("dragleave", function () {
            $(this).removeClass("dragover");
        });

        dropArea.on("drop", function (e) {
            e.preventDefault();
            $(this).removeClass("dragover");

            let files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                let file = files[0];
                if (!isValidFileType(file.name)) {
                    $("#message").text("Invalid file type! Please select an Excel or CSV file.").addClass("show-error");
                    return;
                }

                // Hide any previous error messages
                $("#message").text("").removeClass("show-error");

                // Update the hidden file input
                $("#fileInput")[0].files = files;

                // Update drag-drop area with file name
                $(this).addClass("has-file").html(`
                    <i class="fas fa-file-alt"></i>
                    <div>${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                `);

                checkEnableUploadButton();
            }
        });

        // Ultra modern success popup with enhanced animation
        function showPopup(message, requestId) {
            console.log("Showing popup with message:", message, "and request ID:", requestId);

            // Extract filename from message if possible
            let filename = "";
            if (message.includes("'")) {
                filename = message.split("'")[1].split("'")[0];
            }

            // Format the message similar to the screenshot
            let formattedMessage = `
                <div style="font-size: 1.05rem; margin-bottom: 8px;">
                    The file <span style="font-weight: 500;">'${filename}'</span> was uploaded and processed successfully.
                </div>
                <div style="display: flex; align-items: center; margin-top: 8px; margin-bottom: 4px;">
                    <span style="font-size: 0.9rem; color: #616161; margin-right: 8px;">Request ID:</span>
                    <span style="font-weight: 600; color: #2E7D32; font-size: 0.95rem;">${requestId}</span>
                </div>
            `;

            // Set the formatted message
            $("#successPopup .popup-message").html(formattedMessage);

            // Reset the progress bar with a slight delay for better animation
            setTimeout(function() {
                $("#successPopup .popup-progress").html('<div class="popup-progress-bar"></div>');
            }, 100);

            // Make sure the popup is visible in the DOM
            $("#successPopup").css("display", "block");

            // Show popup with animation
            $("#successPopup").removeClass("hidden").addClass("show");

            console.log("Popup element:", $("#successPopup")[0]);
            console.log("Popup classes:", $("#successPopup").attr("class"));

            // Auto dismiss after 3 seconds (reduced from 5 seconds to match page refresh timing)
            setTimeout(function() {
                $("#successPopup").removeClass("show");
                setTimeout(function() {
                    $("#successPopup").addClass("hidden");
                }, 400);
            }, 3000);
        }

        // Close popup on click of close button with enhanced animation
        $("#successPopup .popup-close").on("click", function() {
            // Add a subtle scale effect when closing
            $("#successPopup").css("transform", "scale(0.98)");
            $("#successPopup").removeClass("show");

            setTimeout(function() {
                $("#successPopup").addClass("hidden");
                // Refresh the page immediately when user closes the popup
                window.location.reload();
            }, 400);
        });

        // Enhanced upload button click event - now shows confirmation modal
        $("#uploadBtn").off("click").on("click", function () {
            let file = $("#fileInput")[0].files[0];
            let supplierId = $("#supplierId").val();
            let requestId = $("#requestId").val();
            let supplierName = supplierMap[supplierId] || "";

            // Validate inputs
            if (!file) {
                $("#message").text("Please select a file.").addClass("show-error");
                return;
            }
            if (!supplierId) {
                $("#message").text("Please select a supplier.").addClass("show-error");
                return;
            }
            if (!requestId) {
                $("#message").text("Please select a request type.").addClass("show-error");
                return;
            }

            // Clear any previous error messages
            $("#message").text("").removeClass("show-error");

            // Show confirmation modal with upload details
            showConfirmationModal(file, supplierId, supplierName, requestId);
        });

        // Function to show confirmation modal with upload details
        function showConfirmationModal(file, supplierId, supplierName, requestId) {
            // Get request type name
            let requestTypeName = $("#requestId option:selected").text();
            let selectedDate = $("#datePicker").val();

            // Format file size
            let fileSize = formatFileSize(file.size);

            // Build upload details HTML
            let detailsHtml = `
                <div style="display: grid; grid-template-columns: 120px 1fr; gap: 12px; font-size: 0.95rem;">
                    <div style="font-weight: 600; color: #495057;">File:</div>
                    <div style="color: #212529; word-break: break-word;">${file.name} <span style="color: #6c757d;">(${fileSize})</span></div>

                    <div style="font-weight: 600; color: #495057;">Supplier:</div>
                    <div style="color: #212529;">${supplierName}</div>

                    <div style="font-weight: 600; color: #495057;">Request Type:</div>
                    <div style="color: #212529;">${requestTypeName}</div>

                    ${selectedDate ? `
                        <div style="font-weight: 600; color: #495057;">Date:</div>
                        <div style="color: #212529;">${selectedDate}</div>
                    ` : ''}
                </div>
            `;

            // Update modal content
            $("#uploadDetails").html(detailsHtml);

            // Prevent body scroll
            $("body").addClass("modal-open");

            // Show the modal with animation
            $("#confirmationModal").removeClass("hidden");
        }

        // Function to close confirmation modal with animation
        function closeConfirmationModal() {
            const modal = $("#confirmationModal")[0];
            const content = modal.querySelector('.confirmation-modal-content');

            // Restore body scroll
            $("body").removeClass("modal-open");

            // Add fade out animation to overlay
            modal.style.animation = 'fadeOut 0.3s ease forwards';

            // Add scale down animation to content
            if (content) {
                content.style.animation = 'scaleDown 0.3s ease forwards';
            }

            // Hide the modal after animation completes
            setTimeout(function() {
                $("#confirmationModal").addClass("hidden");
                // Reset animations for next time
                modal.style.animation = '';
                if (content) {
                    content.style.animation = '';
                }
            }, 300);
        }

        // Confirmation modal event handlers
        $("#confirmationCancel").on("click", function() {
            closeConfirmationModal();
        });

        // Close modal when clicking on overlay (outside the modal content)
        $("#confirmationModal").on("click", function(e) {
            if (e.target === this) {
                closeConfirmationModal();
            }
        });

        // Prevent modal from closing when clicking inside the modal content
        $(".confirmation-modal-content").on("click", function(e) {
            e.stopPropagation();
        });

        // Close modal with ESC key
        $(document).on("keydown", function(e) {
            if (e.key === "Escape" && !$("#confirmationModal").hasClass("hidden")) {
                closeConfirmationModal();
            }
        });

        // Proceed with upload button click event
        $("#confirmationProceed").on("click", function() {
            // Hide the confirmation modal first
            closeConfirmationModal();

            // Proceed with the actual upload after a short delay
            setTimeout(function() {
                performUpload();
            }, 100);
        });

        // Function to perform the actual upload
        function performUpload() {
            let file = $("#fileInput")[0].files[0];
            let supplierId = $("#supplierId").val();
            let requestId = $("#requestId").val();
            let supplierName = supplierMap[supplierId] || "";

            // Show loader and disable button
            showLoader();

            // Prepare form data
            let formData = new FormData();
            formData.append("file", file);
            formData.append("SupplierID", supplierId);
            formData.append("SupplierName", supplierName);
            formData.append("RequestTypeId", requestId);

            // Add CreatedDate if selected (optional)
            let selectedCreatedDate = $("#datePicker").val();
            if (selectedCreatedDate) {
                formData.append("CreatedDate", selectedCreatedDate);
            }

            // Send AJAX request
            $.ajax({
                url: "/api/files/new-upload",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    hideLoader();

                    // Check if the response indicates an error
                    if(response.status_code !== 200){
                         $("#message").text(response.message).addClass("show-error");
                         $("#uploadBtn").prop("disabled", false);
                         return;
                    }

                    // Show success popup and reset form
                    console.log("Upload response:", response); // Debug log

                    try {
                        showPopup(response.data.responses, response.data.task_id);
                    } catch (error) {
                        // Fallback if the response structure is different
                        console.error("Error accessing response data:", error);
                        showPopup("File uploaded successfully", "N/A");
                    }

                    resetForm();

                    // Refresh the page after a short delay to allow the popup to be seen
                    setTimeout(function() {
                        window.location.reload();
                    }, 3000);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    hideLoader();

                    var errorMessage = "Error uploading file.";
                    // If the API returns a JSON error response with a "message" field, use that
                    if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        errorMessage = jqXHR.responseJSON.message;
                    }

                    $("#message").text(errorMessage).addClass("show-error");
                    $("#uploadBtn").prop("disabled", false);
                }
            });
        }

        // Fetch suppliers on page load
        fetchSuppliers();
    });
</script>
{% endblock %}
