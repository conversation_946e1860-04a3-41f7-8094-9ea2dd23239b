{% extends "base.html" %}

{% block title %}Approval Management{% endblock %}
{% block extra_head %}
<!-- Include shared confirmation dialog script -->
<script src="/static/js/shared-confirmation-dialog.js"></script>
<link rel="stylesheet" href="/static/css/pages/approval.css">
{% endblock %}

{% block content %}
<div class="approval-container">
    <div class="approval-header">
        <div class="header-content">
            <h2><i class="fas fa-check-circle"></i> Approval Management</h2>
            <div class="page-description">Review and approve pending data import requests</div>
        </div>
        <div class="refresh-btn">
            <button onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>
    </div>

    <div class="approval-tabs">
        <div class="approval-tab active" data-tab="pending">
            <i class="fas fa-hourglass-half"></i> Pending Requests
            <span class="count">0</span>
        </div>
        <div class="approval-tab" data-tab="approved">
            <i class="fas fa-check"></i> Approved Requests
            <span class="count">0</span>
        </div>
        <div class="approval-tab" data-tab="rejected">
            <i class="fas fa-times"></i> Rejected Requests
            <span class="count">0</span>
        </div>

        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 8px;">
            <button id="processSelectedApproveBtn" style="display: none;" data-action="approve">
              <i class="fas fa-check"></i> Approve
            </button>
            <button id="processSelectedRejectBtn" style="display: none;" data-action="reject">
              <i class="fas fa-times"></i> Reject
            </button>
          </div>  

    </div>

    <div id="pending-tab" class="tab-content active">
        <table class="approval-table" id="pending-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAllCheckbox" /></th>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="pending-requests-body">
                <!-- Pending requests will be loaded here -->
            </tbody>
        </table>
        <div id="pending-empty" class="empty-state" style="display: none;">
            <i class="fas fa-inbox"></i>
            <p>No pending requests found</p>
        </div>
    </div>

    <div id="approved-tab" class="tab-content" style="display: none;">
        <table class="approval-table" id="approved-table">
            <thead>
                <tr>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Approved Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="approved-requests-body">
                <!-- Approved requests will be loaded here -->
            </tbody>
        </table>
        <div id="approved-empty" class="empty-state" style="display: none;">
            <i class="fas fa-check-circle"></i>
            <p>No approved requests found</p>
        </div>
    </div>

    <div id="rejected-tab" class="tab-content" style="display: none;">
        <table class="approval-table" id="rejected-table">
            <thead>
                <tr>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Rejected Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="rejected-requests-body">
                <!-- Rejected requests will be loaded here -->
            </tbody>
        </table>
        <div id="rejected-empty" class="empty-state" style="display: none;">
            <i class="fas fa-times-circle"></i>
            <p>No rejected requests found</p>
        </div>
    </div>
</div>

<!-- We're now using the shared confirmation dialog from shared-confirmation-dialog.js -->

<!-- Notification -->
<div id="notification" class="notification">
    <span id="notification-message"></span>
</div>

{% endblock %}

{% block extra_scripts %}
<script src="/static/js/approval.js"></script>
{% endblock %}
