import pandas as pd
from pymongo import MongoClient
import os

from loggerfile import logger
from config_manager import ConfigManager


config = ConfigManager()
URL = config.get_value("MONGO_CREDENTIAL", "URL")


def db_connection(db_name):
    """Establish a connection to MongoDB and return the client and collection."""
    client = MongoClient(URL)
    db = client[db_name]
    return client, db


def handle_missing_values(df):
    """Handle missing values by replacing NAType with None for MongoDB compatibility."""
    return df.where(pd.notna(df), None)  # Replace NA with None


def insert_data_into_mongodb(db_name, df, coll_name):
    """Insert data from DataFrame into MongoDB collection."""
    try:
        client, db = db_connection(db_name)  # Establish a connection to the MongoDB database
        collection = db[coll_name]
        if not df.empty:
            # Handle missing values before inserting into MongoDB
            df_cleaned = handle_missing_values(df)
            # Convert the DataFrame to a list of dictionaries (records)
            data_to_insert = df_cleaned.to_dict(orient='records')
            # Insert the data into the collection
            result = collection.insert_many(data_to_insert, ordered=False)
            logger.info(f"Inserted {len(data_to_insert)} rows into MongoDB collection {collection.name}.")
            client.close()
            return result.inserted_ids  # This will give a list of inserted _id values
    except Exception as e:
        logger.error(f"An error occurred while inserting data into MongoDB collection {collection.name}:", e)


def read_data_from_mongo(db_name, coll_name):
    logger.info("read_data_from_mongo")
    client, db = db_connection(db_name)  # Establish a connection to the MongoDB database
    collection = db[coll_name]  # Access the specified collection

    # Fetch all documents from the collection
    data = list(collection.find())
    # Close the MongoDB connection
    client.close()

    # Convert the data into a DataFrame
    # MongoDB documents may contain an "_id" field, which is usually not JSON serializable,
    # so we drop or format it if necessary.
    product_df = pd.DataFrame(data)
    if "_id" in product_df.columns:
        product_df["_id"] = product_df["_id"].astype(str)  # Convert "_id" to string if needed
    return product_df
